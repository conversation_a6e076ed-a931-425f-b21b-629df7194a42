import asyncio
asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

import chess
import chess.pgn
import chess.engine
import io
import re
import logging
from typing import List, Tuple, Optional

logger = logging.getLogger(__name__)

class ChessValidator:
    def __init__(self):
        self.engine = None
        # Try different common Stockfish paths
        stockfish_paths = [
            r"D:\atliq_work\coach\image-to-text-main\data\stockfish-windows-x86-64-avx2\stockfish\stockfish-windows-x86-64-avx2.exe"
        ]
        
        for path in stockfish_paths:
            try:
                logger.info(f"Trying Stockfish path: {path}")
                self.engine = chess.engine.SimpleEngine.popen_uci(path)
                logger.info(f"Stockfish found at: {path}")
                break
            except Exception as e:
                logger.warning(f"Stockfish failed at {path}: {str(e)}")
                continue

        if not self.engine:
            logger.warning("Stockfish not found - suggestions will be disabled")
    
    def validate_pgn(self, pgn_text: str) -> Tuple[bool, List[dict]]:
        """Validate PGN and return issues with suggested moves"""
        issues = []
        logger.info(f"Validating PGN: {pgn_text}...")
        
        try:
            # Clean PGN text - remove result markers that might interfere
            clean_pgn = pgn_text.replace('0-1', '').replace('0 - 1', '').replace('1-0', '').replace('1 - 0', '').replace('1/2-1/2', '').strip()
            logger.info(f"Cleaned PGN: {clean_pgn}...")
            
            # Check if game starts from move 1
            # Remove PGN headers to avoid matching dates like "2024.06.29"
            pgn_without_headers = re.sub(r'\[.*?\]\n?', '', clean_pgn)
            first_move_match = re.search(r'(\d+)\.', pgn_without_headers)
            if first_move_match:
                first_move_number = int(first_move_match.group(1))
                if first_move_number > 1:
                    logger.warning(f"Game starts from move {first_move_number}, not move 1")
                    return False, [{
                        "error": f"Incomplete game - starts from move {first_move_number}. Please upload the complete scoresheet starting from move 1.",
                        "suggestions": [],
                        "move_number": first_move_number,
                        "invalid_move": "Missing opening moves"
                    }]
            
            # Parse PGN
            pgn_io = io.StringIO(clean_pgn)
            game = chess.pgn.read_game(pgn_io)
            logger.info(f"Game parsed successfully: {game is not None}")
            
            if not game:
                logger.error("PGN parsing returned None - invalid format")
                return False, [{"error": "Invalid PGN format", "suggestions": []}]
            
            # Count expected moves vs parsed moves
            expected_moves = len(re.findall(r'\d+\.', pgn_text))
            parsed_moves = len(list(game.mainline()))
            logger.info(f"Expected moves: {expected_moves}, Parsed moves: {parsed_moves}")
            
            # If parsing stopped early, there are illegal moves
            if parsed_moves < expected_moves:
                logger.error(f"PGN parsing incomplete: {parsed_moves}/{expected_moves} moves parsed")
                board = chess.Board()
                # Replay moves to find where it stopped
                for i, node in enumerate(game.mainline()):
                    board.push(node.move)
                
                suggestions = self.get_move_suggestions(board)
                # Find the exact move that caused parsing to stop
                moves_text = ""
                for line in clean_pgn.split('\n'):
                    if not line.startswith('[') and line.strip():
                        moves_text += line.strip() + " "
                
                move_pairs = re.findall(r'\d+\.\s*([^\s]+)(?:\s+([^\s]+))?', moves_text)
                failed_move = "Unknown"
                
                # The failed move is at position parsed_moves (0-indexed half-moves)
                half_move_index = 0
                for move_num, (white_move, black_move) in enumerate(move_pairs, 1):
                    if white_move and white_move not in ['1-0', '0-1', '1/2-1/2', '*']:
                        if half_move_index == parsed_moves:
                            failed_move = f"White {move_num} - {white_move}"
                            break
                        half_move_index += 1
                    if black_move and black_move not in ['1-0', '0-1', '1/2-1/2', '*']:
                        if half_move_index == parsed_moves:
                            failed_move = f"Black {move_num} - {black_move}"
                            break
                        half_move_index += 1
                
                issues.append({
                    "move_number": parsed_moves + 1,
                    "invalid_move": failed_move,
                    "position": board.fen(),
                    "suggestions": suggestions,
                    "error": f"Illegal move at {failed_move} - PGN parsing stopped"
                })
                # Return immediately - don't continue validation
                return False, issues
            
            logger.info("PGN parsing successful, proceeding with game-state validation...")
            
            # Now validate by replaying moves step by step against actual game state
            board = chess.Board()
            move_number = 1
            
            # Parse moves from PGN text to compare with parsed game
            moves_text = ""
            for line in clean_pgn.split('\n'):
                if not line.startswith('[') and line.strip():
                    moves_text += line.strip() + " "
            
            # Extract individual moves
            move_pairs = re.findall(r'\d+\.\s*([^\s]+)(?:\s+([^\s]+))?', moves_text)
            logger.info(f"Move pairs for validation: {move_pairs}")
            
            for white_move, black_move in move_pairs:
                # Validate white move
                if white_move and white_move not in ['1-0', '0-1', '1/2-1/2', '*']:
                    logger.info(f"Validating white move {move_number}: '{white_move}'")
                    try:
                        parsed_move = board.parse_san(white_move)
                        logger.info(f"Parsed move: {parsed_move}")
                        if parsed_move not in board.legal_moves:
                            logger.error(f"Move {white_move} not in legal moves")
                            suggestions = self.get_move_suggestions(board)
                            issues.append({
                                "move_number": move_number,
                                "invalid_move": white_move,
                                "position": board.fen(),
                                "suggestions": suggestions,
                                "error": f"Move '{white_move}' is not legal in current position"
                            })
                        else:
                            logger.info(f"Move {white_move} is legal, pushing to board")
                            board.push(parsed_move)
                        move_number += 1
                    except Exception as e:
                        logger.error(f"Failed to parse white move '{white_move}': {str(e)}")
                        suggestions = self.get_move_suggestions(board)
                        issues.append({
                            "move_number": move_number,
                            "invalid_move": white_move,
                            "position": board.fen(),
                            "suggestions": suggestions,
                            "error": f"Cannot parse move '{white_move}': {str(e)}"
                        })
                        break
                
                # Validate black move
                if black_move and black_move not in ['1-0', '0-1', '1/2-1/2', '*']:
                    logger.info(f"Validating black move {move_number}: '{black_move}'")
                    try:
                        parsed_move = board.parse_san(black_move)
                        if parsed_move not in board.legal_moves:
                            suggestions = self.get_move_suggestions(board)
                            issues.append({
                                "move_number": move_number,
                                "invalid_move": black_move,
                                "position": board.fen(),
                                "suggestions": suggestions,
                                "error": f"Move '{black_move}' is not legal in current position"
                            })
                        else:
                            board.push(parsed_move)
                        move_number += 1
                    except Exception as e:
                        logger.error(f"Failed to parse black move '{black_move}': {str(e)}")
                        suggestions = self.get_move_suggestions(board)
                        issues.append({
                            "move_number": move_number,
                            "invalid_move": black_move,
                            "position": board.fen(),
                            "suggestions": suggestions,
                            "error": f"Cannot parse move '{black_move}': {str(e)}"
                        })
                        break
                    
        except Exception as e:
            # Extract the illegal move from the error message
            logger.error(f"Exception during PGN parsing: {str(e)}")
            logger.error(f"Exception type: {type(e).__name__}")
            error_str = str(e)
            illegal_move = "Unknown"
            if "illegal san:" in error_str:
                # Extract move from error like "illegal san: 'Bb4' in ..."
                start = error_str.find("'") + 1
                end = error_str.find("'", start)
                if start > 0 and end > start:
                    illegal_move = error_str[start:end]
            
            # Try to get position where error occurred by parsing moves until failure
            board = chess.Board()
            move_count = 0
            try:
                # Get just the moves part
                moves_text = ""
                for line in clean_pgn.split('\n'):
                    if not line.startswith('[') and line.strip():
                        moves_text += line.strip() + " "
                
                # Parse moves one by one until we hit the illegal one
                moves = re.findall(r'\d+\.\s*([^\s]+)(?:\s+([^\s]+))?', moves_text)
                logger.info(f"Moves found: {moves}")
                logger.info(f"Starting move-by-move validation...")
                for white_move, black_move in moves:
                    if white_move and white_move not in ['1-0', '0-1', '1/2-1/2', '*']:
                        try:
                            move = board.parse_san(white_move)
                            board.push(move)
                            move_count += 1
                            logger.info(f"White move {move_count}: {white_move} - OK")
                        except Exception as ex:
                            logger.error(f"White move failed: {white_move} - {str(ex)}")
                            illegal_move = white_move
                            break
                    if black_move and black_move not in ['1-0', '0-1', '1/2-1/2', '*']:
                        try:
                            move = board.parse_san(black_move)
                            board.push(move)
                            move_count += 1
                            logger.info(f"Black move {move_count}: {black_move} - OK")
                        except Exception as ex:
                            logger.error(f"Black move failed: {black_move} - {str(ex)}")
                            illegal_move = black_move
                            break
            except:
                pass
            
            suggestions = self.get_move_suggestions(board) if self.engine else []
            return False, [{
                "move_number": move_count + 1,
                "invalid_move": illegal_move,
                "position": board.fen(),
                "suggestions": suggestions,
                "error": f"Illegal move '{illegal_move}' at move {move_count + 1}"
            }]
        
        logger.info(f"Validation complete. Issues found: {len(issues)}")
        return len(issues) == 0, issues
    
    # def get_move_suggestions(self, board: chess.Board, num_suggestions: int = 20, depth: int = 25) -> List[str]:
    #     """Get top move suggestions for current position"""
    #     if not self.engine or board.is_game_over():
    #         return []
        
    #     try:
    #         # Get top moves from engine
    #         info = self.engine.analyse(board, chess.engine.Limit(depth=depth), multipv=num_suggestions)
    #         suggestions = []
            
    #         if isinstance(info, list):
    #             for analysis in info:
    #                 if "pv" in analysis and analysis["pv"]:
    #                     move = analysis["pv"][0]
    #                     suggestions.append(board.san(move))
    #         else:
    #             if "pv" in info and info["pv"]:
    #                 move = info["pv"][0]
    #                 suggestions.append(board.san(move))
            
    #         return suggestions[:num_suggestions]
    #     except:
    #         return []

    def get_move_suggestions(self, board: chess.Board, num_suggestions: int = 5) -> List[str]:
        """Get best move suggestions from Stockfish"""
        if not self.engine or board.is_game_over():
            return []
        
        try:
            info_list = self.engine.analyse(
                board, 
                chess.engine.Limit(depth=20),  # Limit(time=2.0)
                multipv=num_suggestions
            )
            
            suggestions = []
            if isinstance(info_list, list):
                for analysis in info_list:
                    if "pv" in analysis and analysis["pv"]:
                        first_move = board.san(analysis["pv"][0])
                        full_line = " ".join(board.san(m) for m in analysis["pv"])
                        score = analysis["score"].pov(board.turn).score(mate_score=100000)
                        suggestions.append({
                            "first_move": first_move,
                            "line": full_line,
                            "score": score
                        })
                
                # Sort by score descending
                suggestions.sort(key=lambda x: x["score"], reverse=True)
                return suggestions[:num_suggestions]
            
            return []
        except Exception as e:
            print("Engine analysis error:", e)
            return []

    
    def __del__(self):
        if hasattr(self, 'engine') and self.engine:
            self.engine.quit()