import streamlit as st
from PIL import Image
import os
import logging
import base64
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage
import datetime
import os
from chess_validator import ChessValidator
from image_preprocessor import ChessImagePreprocessor
from chess_prompts import get_initial_prompt, get_correction_prompt, get_validation_prompt
import chess
import chess.pgn
import io

st.set_page_config(layout="wide")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler("app.log"), logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Set Google Gemini API key
os.environ["GOOGLE_API_KEY"] = st.secrets["GOOGLE_API_KEY"]

def image_to_text(uploaded_file, use_preprocessing=False, use_llm_validation=False):
    """Convert image to text using Gemini with optional preprocessing"""
    logger.info(f"Converting image to text: {uploaded_file.name}, preprocessing: {use_preprocessing}")
    try:
        image_bytes = uploaded_file.getvalue()
        
        # Apply preprocessing if enabled
        if use_preprocessing:
            preprocessor = ChessImagePreprocessor()
            results = preprocessor.preprocess_image(image_bytes)
            image_bytes = results['processed_image']
            
            # Save processed image for debugging
            import os
            debug_folder = "processed_images"
            os.makedirs(debug_folder, exist_ok=True)
            
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            processed_filename = f"{debug_folder}/processed_{timestamp}_{uploaded_file.name}"
            
            with open(processed_filename, 'wb') as f:
                f.write(image_bytes)
            
            st.info(f"📁 Processed image saved: {processed_filename}")
            
            if results['warnings']:
                st.warning(f"⚠️ {results['warnings'][0]}")
        
        base64_image = base64.b64encode(image_bytes).decode("utf-8")
        
        model = ChatGoogleGenerativeAI(model="gemini-2.5-pro", temperature=0)
        message = HumanMessage(
            content=[
                {
                    "type": "text", 
                    "text": get_initial_prompt()
                },
                {
                    "type": "image_url",
                    "image_url": f"data:image/{uploaded_file.type.split('/')[1]};base64,{base64_image}"
                },
            ]
        )
        result = model.invoke([message]).content
        # logger.info(f"Initial OCR Response: {message}")
        print(f"Initial OCR result: {result}")
        logger.info(f"Initial RAW OCR result: {result}")

        if not result or len(result)==0:
            logger.info("Trying with a different model")

            model = ChatGoogleGenerativeAI(model="gemini-2.0-flash", temperature=0)
            message = HumanMessage(
                content=[
                    {
                        "type": "text", 
                        "text": get_initial_prompt()
                    },
                    {
                        "type": "image_url",
                        "image_url": f"data:image/{uploaded_file.type.split('/')[1]};base64,{base64_image}"
                    },
                ]
            )
            result = model.invoke([message]).content
            # logger.info(f"Initial OCR Response: {message}")
            print(f"Fallback OCR result: {result}")
            logger.info(f"Fallback RAW OCR result: {result}")

        
        # Clean up response format
        if result.startswith("```"):
            lines = result.split('\n')
            if lines[0].startswith('```'):
                result = '\n'.join(lines[1:])
            if result.endswith('```'):
                result = result[:-3]
        
        initial_result = result.strip()
        logger.info(f"Initial cleaned OCR result: {initial_result}")

        # Apply LLM validation if enabled
        if use_llm_validation:
            validator = ChessValidator()
            is_valid, issues = validator.validate_pgn(initial_result)
            
            if not is_valid and issues:
                # Try to correct first issue with LLM
                for issue in issues[:1]:  # Only first issue
                    if issue.get('invalid_move'):
                        correction = correct_move_with_llm(
                            uploaded_file,
                            issue['invalid_move'],
                            issue.get('move_number', 1),
                            use_preprocessing
                        )
                        if correction:
                            initial_result = initial_result.replace(
                                issue['invalid_move'], correction, 1
                            )
                            st.toast(f"Auto-corrected: {issue['invalid_move']} → {correction}", icon="🔧")
                            break
        
        return initial_result
    except Exception as e:
        logger.error(f"Error in image processing: {str(e)}")
        raise

def correct_move_with_llm(uploaded_file, illegal_move, move_number, use_preprocessing=False):
    """Correct a specific illegal move using LLM"""
    try:
        image_bytes = uploaded_file.getvalue()
        
        if use_preprocessing:
            preprocessor = ChessImagePreprocessor()
            results = preprocessor.preprocess_image(image_bytes)
            image_bytes = results['processed_image']
        
        base64_image = base64.b64encode(image_bytes).decode("utf-8")
        
        model = ChatGoogleGenerativeAI(model="gemini-2.0-flash", temperature=0)
        message = HumanMessage(
            content=[
                {
                    "type": "text", 
                    "text": get_correction_prompt(illegal_move, move_number)
                },
                {
                    "type": "image_url",
                    "image_url": f"data:image/{uploaded_file.type.split('/')[1]};base64,{base64_image}"
                },
            ]
        )
        result = model.invoke([message]).content.strip()
        print(f"LLM correction result: {result}")   
        if result == "KEEP_ORIGINAL":
            return None
        
        # Validate the correction is reasonable (single move format)
        import re
        if re.match(r'^[KQRBN]?[a-h]?[1-8]?[x]?[a-h][1-8][=QRBN]?[+#]?$|^O-O(-O)?[+#]?$', result):
            return result
        
        return None
    except Exception as e:
        logger.error(f"Error in move correction: {str(e)}")
        return None

def main():
    logger.info("Application started")
    st.title("📸 Snap & Convert")
    st.markdown("**Extract Text from Images with Instant Markdown Preview**")
    st.markdown("---")

    # Helper function to update text content and store original
    def update_text_content(new_content):
        # Store original if this is the first time setting content or if we don't have an original
        if 'original_text_content' not in st.session_state or not st.session_state.original_text_content:
            st.session_state.original_text_content = new_content
        st.session_state.text_content = new_content

    # Initialize session state
    if "text_content" not in st.session_state:
        st.session_state.text_content = ""
    if "original_text_content" not in st.session_state:
        st.session_state.original_text_content = ""
    if "chess_validator" not in st.session_state:
        st.session_state.chess_validator = ChessValidator()
    if "validation_issues" not in st.session_state:
        st.session_state.validation_issues = []
    if "use_preprocessing" not in st.session_state:
        st.session_state.use_preprocessing = False
    if "use_llm_validation" not in st.session_state:
        st.session_state.use_llm_validation = False
    if "uploaded_file" not in st.session_state:
        st.session_state.uploaded_file = None
    if "current_move" not in st.session_state:
        st.session_state.current_move = 0
    if "game_moves" not in st.session_state:
        st.session_state.game_moves = []

    # Sidebar Configuration
    with st.sidebar:
        st.header("⚙️ Configuration")
        
        # Preprocessing Toggle
        with st.expander("🔧 Image Processing", expanded=True):
            st.session_state.use_preprocessing = st.checkbox(
                "Enable Image Preprocessing",
                value=st.session_state.use_preprocessing,
                help="Apply glare reduction, perspective correction, and contrast enhancement"
            )
            
            if st.session_state.use_preprocessing:
                st.info("✅ Preprocessing: Glare reduction, geometric corrections, OCR enhancement")
            else:
                st.info("📷 Using original image without preprocessing")
            
            st.session_state.use_llm_validation = st.checkbox(
                "Enable LLM Validation",
                value=st.session_state.use_llm_validation,
                help="Automatically correct illegal moves using AI re-examination"
            )
            
            if st.session_state.use_llm_validation:
                st.info("🤖 LLM will auto-correct illegal moves")
            else:
                st.info("⚡ Fast mode - no auto-correction")
        
        with st.expander("📂 File Management", expanded=True):
            loaded_file = st.file_uploader("Upload existing PGN:", 
                                         type=["txt", "pgn"],
                                         help="Upload existing PGN files to edit")
            if loaded_file:
                try:
                    update_text_content(loaded_file.getvalue().decode())
                    st.toast("File loaded successfully!", icon="✅")
                except Exception as e:
                    st.error(f"Load error: {str(e)}")

    # Main Interface
    col1, col2 = st.columns([1, 1], gap="large")

    with col1:
        st.subheader("🖼️ Image Upload")
        img_files = st.file_uploader("Select chess scoresheet images:", 
                                   type=["jpg", "jpeg", "png"],
                                   accept_multiple_files=True,
                                   label_visibility="collapsed",
                                   help="Upload multiple images for multi-page scoresheets")
        
        if img_files:
            st.session_state.uploaded_file = img_files[0] if img_files else None
            
            # Show all uploaded images
            if len(img_files) > 1:
                st.info(f"📄 {len(img_files)} pages uploaded")
                
                # Show images in tabs
                # tabs = st.tabs([f"Page {i+1}" for i in range(len(img_files))])
                tabs = st.tabs([img_file.name for img_file in img_files])
                for _, (tab, img_file) in enumerate(zip(tabs, img_files)):
                    with tab:
                        st.image(img_file, use_container_width=True)
                        
                # Check for multiple games after showing images
                if "pgn_extractions" in st.session_state and len(st.session_state.pgn_extractions) > 1:
                    from multi_image_processor import MultiImageProcessor
                    processor = MultiImageProcessor()
                    
                    # Check if pages are sequential or separate games
                    page_starts = []
                    for i, pgn in enumerate(st.session_state.pgn_extractions):
                        import re
                        # Remove PGN headers to avoid matching dates like "2024.06.29"
                        pgn_without_headers = re.sub(r'\[.*?\]\n?', '', pgn)
                        first_move_match = re.search(r'(\d+)\.', pgn_without_headers)
                        start_move = int(first_move_match.group(1)) if first_move_match else 1
                        page_starts.append((i+1, start_move))
                    print("Page starts", page_starts)

                    # Check for overlaps or multiple games starting from 1
                    games_starting_from_1 = [page for page, start in page_starts if start == 1]
                    is_continuous, continuity_errors = processor.validate_multi_page_continuity(st.session_state.pgn_extractions)
                    has_overlaps = any("Overlap" in error for error in continuity_errors)
                    
                    if len(games_starting_from_1) > 1 or has_overlaps:
                        # Multiple games detected - show selection UI
                        st.error("🚫 Multiple games detected!")
                        if len(games_starting_from_1) > 1:
                            # st.warning(f"Pages {games_starting_from_1} all start from move 1. This suggests different games, not sequential pages.")
                            st.warning(f"Images suggests different games, not sequential pages.")
                        if has_overlaps:
                            st.warning("Images have overlapping moves, indicating separate games.")
                        
                        # Let user select which game to process
                        selected_page = st.selectbox(
                            "Select which game to analyze:",
                            options=range(len(img_files)),
                            # format_func=lambda x: f"Page {x+1} (starts from move {page_starts[x][1]})"
                            format_func=lambda x: f"{img_files[x].name} (starts from move {page_starts[x][1]})"
                        )
                        
                        if st.button("Process Selected Game", key="process_selected"):
                            update_text_content(st.session_state.pgn_extractions[selected_page])
                            st.success(f"✅ Processing {img_files[selected_page].name} only")
                            st.session_state.validation_issues = []
                            # Clear pgn_extractions to prevent showing selection UI again
                            del st.session_state.pgn_extractions
                            st.toast("Game selection complete!", icon="✅")
                            st.rerun()
                    else:
                        # Pages are sequential - combine them
                        st.info("📋 Sequential pages detected - combining into single game")
                        combined_pgn, warnings = processor.combine_pgn_pages(st.session_state.pgn_extractions)
                        update_text_content(combined_pgn)
                        
                        # Show warnings if any
                        for warning in warnings:
                            st.warning(f"⚠️ {warning}")
                        
                        # Clear pgn_extractions after combining
                        del st.session_state.pgn_extractions
            else:
                with st.container(border=True):
                    st.image(img_files[0], use_container_width=True)
        
            with st.container(border=True):
                col_extract, col_auto = st.columns(2)
                with col_extract:
                    if st.button("✨ Extract Text", use_container_width=True):
                        with st.spinner(f"🔍 Extracting from {len(img_files)} page(s)..."):
                            try:
                                if len(img_files) == 1:
                                    # Single image
                                    extracted_text = image_to_text(
                                        img_files[0],
                                        st.session_state.use_preprocessing,
                                        st.session_state.use_llm_validation
                                    )
                                    update_text_content(extracted_text)
                                else:
                                    # Multiple images - first extract and check if they're sequential
                                    from multi_image_processor import MultiImageProcessor
                                    processor = MultiImageProcessor()
                                    
                                    pgn_extractions = []
                                    for i, img_file in enumerate(img_files):
                                        st.info(f"Processing page {i+1}/{len(img_files)}...")
                                        pgn = image_to_text(
                                            img_file,
                                            st.session_state.use_preprocessing,
                                            st.session_state.use_llm_validation
                                        )
                                        pgn_extractions.append(pgn)
                                    
                                    # Store extractions in session state for later use
                                    st.session_state.pgn_extractions = pgn_extractions
                                    
                                    # Force rerun to show game selection UI
                                    st.rerun()
                                
                                st.session_state.validation_issues = []
                                st.toast("Text extraction complete!", icon="✅")
                            except Exception as e:
                                st.error(f"Extraction failed: {str(e)}")
                
                with col_auto:
                    if st.button("🤖 Auto-Correct", use_container_width=True):
                        with st.spinner("🔄 Extracting and validating..."):
                            try:
                                # Extract text
                                initial_pgn = image_to_text(
                                    img_file, 
                                    st.session_state.use_preprocessing,
                                    st.session_state.use_llm_validation
                                )
                                
                                # Validate with fresh validator
                                validator = ChessValidator()
                                is_valid, issues = validator.validate_pgn(initial_pgn)
                                
                                if is_valid:
                                    update_text_content(initial_pgn)
                                    st.toast("Perfect extraction - no corrections needed!", icon="✅")
                                else:
                                    # Try to correct first issue
                                    corrected_pgn = initial_pgn
                                    for issue in issues[:1]:  # Only correct first issue
                                        if issue.get('invalid_move'):
                                            correction = correct_move_with_llm(
                                                img_file, 
                                                issue['invalid_move'], 
                                                issue.get('move_number', 1),
                                                st.session_state.use_preprocessing
                                            )
                                            if correction:
                                                # Simple replacement (production would need better PGN editing)
                                                corrected_pgn = corrected_pgn.replace(
                                                    issue['invalid_move'], correction, 1
                                                )
                                                st.toast(f"Corrected {issue['invalid_move']} → {correction}", icon="🔧")
                                                break
                                    
                                    update_text_content(corrected_pgn)
                                    
                                st.session_state.validation_issues = []
                            except Exception as e:
                                st.error(f"Auto-correction failed: {str(e)}")

    with col2:
        st.subheader("✍️ PGN Editor")

        # Format controls
        col_format, col_revert = st.columns([3, 1])
        with col_format:
            format_moves_newline = st.checkbox(
                "📝 Format moves on separate lines",
                value=False,
                help="Display each move pair on a new line for better readability"
            )
        with col_revert:
            if st.button("🔄 Original Format", help="Revert to original PGN format"):
                # Store the original format in session state if not already stored
                if 'original_text_content' not in st.session_state:
                    st.session_state.original_text_content = st.session_state.text_content
                # Revert to original format
                st.session_state.text_content = st.session_state.original_text_content
                st.rerun()

        # Function to format PGN with moves on separate lines
        def format_pgn_moves(pgn_text):
            if not pgn_text.strip():
                return pgn_text

            import re
            lines = pgn_text.split('\n')
            formatted_lines = []

            for line in lines:
                # Keep tag lines (lines starting with [) and empty lines unchanged
                if line.strip().startswith('[') or not line.strip():
                    formatted_lines.append(line)
                else:
                    # Process move lines - extract move pairs
                    # Pattern to match: number. white_move black_move
                    move_pattern = r'(\d+)\.\s*([^\s]+)(?:\s+([^\s]+))?'
                    matches = re.findall(move_pattern, line)

                    if matches:
                        for move_num, white_move, black_move in matches:
                            # Create line with move number and both moves if available
                            if black_move and black_move not in ['1-0', '0-1', '1/2-1/2', '*']:
                                formatted_lines.append(f"{move_num}. {white_move} {black_move}")
                            elif white_move not in ['1-0', '0-1', '1/2-1/2', '*']:
                                formatted_lines.append(f"{move_num}. {white_move}")
                    else:
                        # If no move pattern found, keep the line as is
                        formatted_lines.append(line)

            return '\n'.join(formatted_lines)

        # Apply formatting if checkbox is checked
        display_text = st.session_state.text_content
        if format_moves_newline and display_text:
            display_text = format_pgn_moves(display_text)
            # Show a small preview of the formatting change
            if display_text != st.session_state.text_content:
                st.info("📝 Moves are now formatted on separate lines. Use 'Original Format' button to revert.")

        edited_text = st.text_area(
            "Edit your PGN:",
            value=display_text,
            height=400,
            key="editor",
            label_visibility="collapsed"
        )

        # Chess Controls
        if edited_text:
            col1, col2, col3, col4 = st.columns([1, 1, 1, 1])
            with col1:
                if st.button("🧠 Smart Validate", use_container_width=True):
                    # Use smart validation with cascading error detection
                    validator = ChessValidator()
                    is_valid, issues = validator.smart_validate_pgn(edited_text)
                    st.session_state.validation_issues = issues
                    if is_valid:
                        st.success("✅ Perfect game!")
                    else:
                        # Check if we have cascading errors
                        cascading_errors = [issue for issue in issues if issue.get('type') == 'cascading_error']
                        if cascading_errors:
                            st.error(f"🔗 Cascading error detected! Fix move {cascading_errors[0]['move_number']} first.")
                        else:
                            st.warning(f"⚠️ {len(issues)} individual issues")

            with col2:
                if st.button("▶️ Play Game", use_container_width=True):
                    try:
                        clean_pgn = edited_text.replace('0-1', '').replace('1-0', '').replace('1/2-1/2', '').strip()
                        pgn_io = io.StringIO(clean_pgn)
                        game = chess.pgn.read_game(pgn_io)
                        if game:
                            st.session_state.game_moves = list(game.mainline())
                            st.session_state.current_move = 0
                        else:
                            st.error("Cannot parse PGN - check for invalid moves")
                    except Exception as e:
                        st.error(f"Invalid PGN: {str(e)}")
            
            with col3:
                if st.button("📊 Move Analysis", use_container_width=True):
                    # Show move-by-move analysis
                    try:
                        validator = ChessValidator()
                        is_valid, issues = validator.smart_validate_pgn(edited_text)

                        if issues:
                            st.subheader("📊 Move-by-Move Analysis")

                            # Extract moves for analysis
                            import re
                            clean_pgn = edited_text.replace('0-1', '').replace('1-0', '').replace('1/2-1/2', '').strip()
                            moves_text = ""
                            for line in clean_pgn.split('\n'):
                                if not line.startswith('[') and line.strip():
                                    moves_text += line.strip() + " "

                            move_pairs = re.findall(r'(\d+)\.\s*([^\s]+)(?:\s+([^\s]+))?', moves_text)

                            # Show analysis table
                            analysis_data = []
                            board = chess.Board()

                            for move_num, white_move, black_move in move_pairs:
                                move_num = int(move_num)

                                # Check white move
                                if white_move and white_move not in ['1-0', '0-1', '1/2-1/2', '*']:
                                    try:
                                        parsed_move = board.parse_san(white_move)
                                        if parsed_move in board.legal_moves:
                                            board.push(parsed_move)
                                            status = "✅ Valid"
                                        else:
                                            status = "❌ Illegal"
                                    except:
                                        status = "❌ Invalid notation"

                                    analysis_data.append({
                                        "Move": f"{move_num}. {white_move}",
                                        "Color": "White",
                                        "Status": status
                                    })

                                # Check black move
                                if black_move and black_move not in ['1-0', '0-1', '1/2-1/2', '*']:
                                    try:
                                        parsed_move = board.parse_san(black_move)
                                        if parsed_move in board.legal_moves:
                                            board.push(parsed_move)
                                            status = "✅ Valid"
                                        else:
                                            status = "❌ Illegal"
                                    except:
                                        status = "❌ Invalid notation"

                                    analysis_data.append({
                                        "Move": f"{move_num}... {black_move}",
                                        "Color": "Black",
                                        "Status": status
                                    })

                            # Display as dataframe
                            import pandas as pd
                            df = pd.DataFrame(analysis_data)
                            st.dataframe(df, use_container_width=True)

                            # Show summary
                            valid_moves = len([row for row in analysis_data if "✅" in row["Status"]])
                            total_moves = len(analysis_data)
                            st.metric("Valid Moves", f"{valid_moves}/{total_moves}", f"{(valid_moves/total_moves*100):.1f}%")
                        else:
                            st.success("✅ All moves are valid!")

                    except Exception as e:
                        st.error(f"Analysis failed: {str(e)}")

            with col4:
                if st.button("📋 Final Position", use_container_width=True):
                    try:
                        clean_pgn = edited_text.replace('0-1', '').replace('1-0', '').replace('1/2-1/2', '').strip()
                        pgn_io = io.StringIO(clean_pgn)
                        game = chess.pgn.read_game(pgn_io)
                        if game:
                            board = game.end().board()
                            st.markdown(f'<div style="text-align: center;">{board._repr_svg_()}</div>', unsafe_allow_html=True)
                        else:
                            st.error("Cannot parse PGN - check for invalid moves")
                    except Exception as e:
                        st.error(f"Invalid PGN: {str(e)}")
        
        # Game Playback
        if st.session_state.game_moves:
            st.subheader("▶️ Game Playback")
            
            # Navigation buttons
            col_prev, col_next, col_reset = st.columns([1, 1, 1])
            with col_prev:
                if st.button("⬅️ Previous", disabled=st.session_state.current_move <= 0):
                    st.session_state.current_move = max(0, st.session_state.current_move - 1)
            with col_next:
                if st.button("➡️ Next", disabled=st.session_state.current_move >= len(st.session_state.game_moves)):
                    st.session_state.current_move = min(len(st.session_state.game_moves), st.session_state.current_move + 1)
            with col_reset:
                if st.button("🔄 Reset"):
                    st.session_state.current_move = 0
            
            # Show current position
            try:
                board = chess.Board()
                
                for i, node in enumerate(st.session_state.game_moves[:st.session_state.current_move]):
                    board.push(node.move)
                
                # Display move info
                if st.session_state.current_move > 0:
                    current_node = st.session_state.game_moves[st.session_state.current_move - 1]
                    temp_board = chess.Board()
                    for node in st.session_state.game_moves[:st.session_state.current_move - 1]:
                        temp_board.push(node.move)
                    last_move = temp_board.san(current_node.move)
                    
                    # Calculate proper chess move number
                    chess_move_number = (st.session_state.current_move + 1) // 2
                    is_white_move = st.session_state.current_move % 2 == 1
                    color = "White" if is_white_move else "Black"
                    
                    st.info(f"Move {chess_move_number} ({color}): {last_move}")
                else:
                    st.info("Starting position")
                
                # Display board
                st.markdown(f'<div style="text-align: center;">{board._repr_svg_()}</div>', unsafe_allow_html=True)
                
            except Exception as e:
                st.error(f"Playback error: {str(e)}")
        
        # Display validation issues with correction options
        if st.session_state.validation_issues:
            st.subheader("🔧 Issues & Corrections")

            # Check for cascading errors first
            cascading_errors = [issue for issue in st.session_state.validation_issues if issue.get('type') == 'cascading_error']
            individual_errors = [issue for issue in st.session_state.validation_issues if issue.get('type') != 'cascading_error']

            if cascading_errors:
                st.error("🔗 **Cascading Error Detected!**")
                col_info, col_batch = st.columns([3, 1])
                with col_info:
                    st.info("💡 **Quick Fix Strategy**: Fix the first error below, and it will likely resolve most subsequent issues automatically.")
                with col_batch:
                    if st.button("🚀 Auto-Fix Primary", help="Automatically apply the best suggestion for the primary error"):
                        primary_error = cascading_errors[0]
                        if primary_error.get('suggestions'):
                            suggestions = primary_error['suggestions']
                            if suggestions:
                                best_suggestion = suggestions[0]
                                if isinstance(best_suggestion, dict):
                                    fix_move = best_suggestion['first_move']
                                else:
                                    fix_move = best_suggestion

                                new_text = edited_text.replace(primary_error['invalid_move'], fix_move, 1)
                                st.session_state.text_content = new_text
                                st.session_state.validation_issues = []
                                st.toast(f"Auto-fixed: {primary_error['invalid_move']} → {fix_move}", icon="🚀")
                                st.rerun()

                for i, issue in enumerate(cascading_errors):
                    with st.expander(f"🎯 PRIMARY ERROR - {issue['color'].title()} Move {issue['move_number']}: {issue['invalid_move']}", expanded=True):
                        st.write(f"**Problem:** {issue.get('error', 'Unknown error')}")
                        st.write(f"**Impact:** {issue.get('cascading_count', 0)} subsequent moves are invalid due to this error")
                        st.write(f"**Recovery:** {issue.get('recovery_suggestion', 'Fix this move first')}")

                        if issue.get('suggestions'):
                            st.write("**🎯 Recommended fixes:**")
                            # Handle both old format (list of strings) and new format (list of dicts)
                            suggestions = issue['suggestions']
                            if suggestions and isinstance(suggestions[0], dict):
                                # New format with detailed suggestions
                                for j, suggestion in enumerate(suggestions[:3]):  # Show top 3
                                    col1, col2 = st.columns([3, 1])
                                    with col1:
                                        st.write(f"**{suggestion['first_move']}** (Score: {suggestion.get('score', 'N/A')})")
                                        st.caption(f"Line: {suggestion.get('line', suggestion['first_move'])}")
                                    with col2:
                                        if st.button(f"Use {suggestion['first_move']}", key=f"cascade_suggest_{i}_{j}"):
                                            new_text = edited_text.replace(issue['invalid_move'], suggestion['first_move'], 1)
                                            st.session_state.text_content = new_text
                                            st.session_state.validation_issues = []
                                            st.toast(f"Applied fix: {issue['invalid_move']} → {suggestion['first_move']}", icon="🔧")
                                            st.rerun()
                            else:
                                # Old format - list of strings
                                cols = st.columns(min(len(suggestions), 3))
                                for j, suggestion in enumerate(suggestions[:3]):
                                    with cols[j]:
                                        if st.button(f"Use: {suggestion}", key=f"cascade_suggest_{i}_{j}"):
                                            new_text = edited_text.replace(issue['invalid_move'], suggestion, 1)
                                            st.session_state.text_content = new_text
                                            st.session_state.validation_issues = []
                                            st.toast(f"Applied fix: {issue['invalid_move']} → {suggestion}", icon="🔧")
                                            st.rerun()

            if individual_errors:
                if cascading_errors:
                    st.subheader("📝 Individual Issues")
                    st.info("💡 These may resolve automatically after fixing the cascading error above.")

                for i, issue in enumerate(individual_errors):
                    with st.expander(f"Issue {i+1}: {issue.get('error', 'Unknown error')}", expanded=not bool(cascading_errors)):
                        st.write(f"**Problem:** {issue.get('invalid_move', 'Unknown move')}")

                        if issue.get('suggestions'):
                            st.write("**Stockfish suggestions:**")
                            suggestions = issue['suggestions']
                            if suggestions and isinstance(suggestions[0], dict):
                                # New format
                                for j, suggestion in enumerate(suggestions[:3]):
                                    col1, col2 = st.columns([3, 1])
                                    with col1:
                                        st.write(f"**{suggestion['first_move']}**")
                                    with col2:
                                        if st.button(f"Use {suggestion['first_move']}", key=f"individual_suggest_{i}_{j}"):
                                            new_text = edited_text.replace(issue['invalid_move'], suggestion['first_move'], 1)
                                            st.session_state.text_content = new_text
                                            st.session_state.validation_issues = []
                                            st.rerun()
                            else:
                                # Old format
                                cols = st.columns(min(len(suggestions), 3))
                                for j, suggestion in enumerate(suggestions[:3]):
                                    with cols[j]:
                                        if st.button(f"Use: {suggestion}", key=f"individual_suggest_{i}_{j}"):
                                            new_text = edited_text.replace(issue['invalid_move'], suggestion, 1)
                                            st.session_state.text_content = new_text
                                            st.session_state.validation_issues = []
                                            st.rerun()
                    
                    # LLM correction option
                    if st.session_state.uploaded_file and issue.get('invalid_move'):
                        if st.button(f"🤖 AI Re-examine", key=f"ai_correct_{i}"):
                            with st.spinner("Re-examining image..."):
                                correction = correct_move_with_llm(
                                    st.session_state.uploaded_file,
                                    issue['invalid_move'],
                                    issue.get('move_number', 1),
                                    st.session_state.use_preprocessing
                                )
                                if correction:
                                    new_text = edited_text.replace(issue['invalid_move'], correction, 1)
                                    st.session_state.text_content = new_text
                                    st.session_state.validation_issues = []  # Clear issues after fix
                                    st.toast(f"Corrected: {issue['invalid_move']} → {correction}", icon="🔧")
                                    st.rerun()
                                else:
                                    st.info("AI confirmed the original text is correct as written")

        # Save Controls
        if edited_text:
            st.subheader("💾 Save & Export")
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            save_name = st.text_input("Filename:", f"chess_game_{timestamp}.pgn")
            
            col_save, col_download = st.columns(2)
            with col_save:
                if st.button("💾 Save Locally", use_container_width=True):
                    try:
                        with open(save_name, "w") as f:
                            f.write(edited_text)
                        st.toast(f"Saved to {save_name}", icon="💾")
                    except Exception as e:
                        st.error(f"Save failed: {str(e)}")
            
            with col_download:
                st.download_button(
                    label="⬇️ Download PGN",
                    data=edited_text,
                    file_name=save_name,
                    mime="application/x-chess-pgn",
                    use_container_width=True
                )

    # Update session state
    st.session_state.text_content = edited_text

if __name__ == "__main__":
    main()
    logger.info("Application ended")